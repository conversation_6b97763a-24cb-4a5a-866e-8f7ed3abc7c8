package com.mlc.ai.task.scheduler.manager;

import com.mlc.ai.task.scheduler.status.SchedulerStatusEvent;
import com.mlc.ai.task.scheduler.status.SchedulerStatus;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 调度器状态管理器
 * 负责管理调度器的状态变化和状态事件，以及处理状态变化时的相应操作
 */
@Slf4j
@Getter
public class SchedulerStatusManager {

    /**
     * 调度器ID
     */
    private final String schedulerId;

    /**
     * 任务队列管理器，用于状态变化时的队列操作
     */
    private final TaskQueueManager queueManager;

    /**
     * 任务依赖检查器，用于状态变化时的任务操作
     */
    private final TaskDependencyChecker dependencyChecker;

    /**
     * 当前状态
     */
    private volatile SchedulerStatus currentStatus = SchedulerStatus.IDLE;

    /**
     * 整体任务执行是否完成
     */
    private final AtomicBoolean completed = new AtomicBoolean(false);

    /**
     * 完成回调列表
     */
    private final List<Runnable> completionCallbacks = new ArrayList<>();

    /**
     * 调度器状态变化事件流
     */
    private final Flux<SchedulerStatusEvent> statusFlux;

    /**
     * 调度器状态变化事件接收器
     */
    private FluxSink<SchedulerStatusEvent> statusSink;

    /**
     * 构造函数
     *
     * @param schedulerId 调度器ID
     * @param queueManager 任务队列管理器
     * @param dependencyChecker 任务依赖检查器
     */
    public SchedulerStatusManager(String schedulerId, TaskQueueManager queueManager, TaskDependencyChecker dependencyChecker) {
        this.schedulerId = schedulerId;
        this.queueManager = queueManager;
        this.dependencyChecker = dependencyChecker;

        this.statusFlux = Flux.create(sink -> {
            this.statusSink = sink;
            // 订阅自己的状态变化事件来处理状态变化逻辑
            if (queueManager != null && dependencyChecker != null) {
                sink.onRequest(n -> {
                    // 当有订阅者时，开始处理状态变化事件
                });
            }
        });
    }

    /**
     * 通知状态变化
     *
     * @param newStatus 新状态
     */
    public void changeStatus(SchedulerStatus newStatus) {
        SchedulerStatus oldStatus = this.currentStatus;
        this.currentStatus = newStatus;

        // 创建状态变化事件
        SchedulerStatusEvent event = new SchedulerStatusEvent(schedulerId, oldStatus, newStatus);

        // 处理状态变化逻辑
        this.handleStatusChange(event);

        // 发送状态变化事件
        if (statusSink != null) {
            statusSink.next(event);
        }

        log.debug("[{}] 调度器状态变更: {} -> {}", schedulerId, oldStatus, newStatus);
    }

    /**
     * 处理调度器状态变化事件
     *
     * @param event 调度器状态变化事件
     */
    private void handleStatusChange(SchedulerStatusEvent event) {
        // 如果没有依赖的管理器，则跳过处理
        if (queueManager == null || dependencyChecker == null) {
            return;
        }

        log.info("[{}] 调度器状态变更: {} -> {}", schedulerId, event.getOldStatus(), event.getNewStatus());

        // 根据不同的状态变化执行相应的操作
        SchedulerStatus newStatus = event.getNewStatus();

        if (newStatus == SchedulerStatus.FAILED) {
            // 清空准备队列
            queueManager.clearQueue();
        } else if (newStatus == SchedulerStatus.CANCELLED) {
            // 清空准备队列
            queueManager.clearQueue();

            // 取消所有运行中的任务
            dependencyChecker.cancelRunningTasks();

            // 设置完成状态
            this.setCompleted(true);
        }
    }

    /**
     * 注册完成回调
     *
     * @param callback 回调函数
     */
    public void registerCompletionCallback(Runnable callback) {
        completionCallbacks.add(callback);
    }

    /**
     * 触发完成回调
     */
    public void notifyCompletion() {
        completionCallbacks.forEach(callback -> {
            try {
                callback.run();
            } catch (Exception e) {
                log.error("[{}] 执行完成回调时发生异常", schedulerId, e);
            }
        });
    }

    /**
     * 检查是否已完成
     *
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return completed.get();
    }

    /**
     * 设置完成状态
     *
     * @param completed 是否完成
     */
    public void setCompleted(boolean completed) {
        this.completed.set(completed);
    }

    /**
     * 检查是否可以启动
     *
     * @return 是否可以启动
     */
    public boolean canStart() {
        return currentStatus == SchedulerStatus.IDLE;
    }

    /**
     * 检查是否正在运行
     *
     * @return 是否正在运行
     */
    public boolean isRunning() {
        return currentStatus == SchedulerStatus.RUNNING;
    }

    /**
     * 检查是否已失败
     *
     * @return 是否已失败
     */
    public boolean isFailed() {
        return currentStatus == SchedulerStatus.FAILED;
    }
}
